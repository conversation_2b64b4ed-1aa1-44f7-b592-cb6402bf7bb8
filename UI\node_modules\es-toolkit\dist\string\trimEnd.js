'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

function trimEnd(str, chars) {
    if (chars === undefined) {
        return str.trimEnd();
    }
    let endIndex = str.length;
    switch (typeof chars) {
        case 'string': {
            if (chars.length !== 1) {
                throw new Error(`The 'chars' parameter should be a single character string.`);
            }
            while (endIndex > 0 && str[endIndex - 1] === chars) {
                endIndex--;
            }
            break;
        }
        case 'object': {
            while (endIndex > 0 && chars.includes(str[endIndex - 1])) {
                endIndex--;
            }
        }
    }
    return str.substring(0, endIndex);
}

exports.trimEnd = trimEnd;

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api';

export interface ApiResponse<T> {
  message: string;
  data: T;
}

export interface ApiError {
  error: string;
  details?: any;
}

class ApiService {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData: ApiError = await response.json().catch(() => ({
        error: `HTTP ${response.status}: ${response.statusText}`,
      }));
      throw new Error(errorData.error || 'An error occurred');
    }

    const data: ApiResponse<T> = await response.json();
    return data.data;
  }

  async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
    const url = new URL(`${this.baseURL}${endpoint}`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value) url.searchParams.append(key, value);
      });
    }

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    return this.handleResponse<T>(response);
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse<T>(response);
  }

  async postFormData<T>(endpoint: string, formData: FormData): Promise<T> {
    const token = localStorage.getItem('authToken');
    const headers: HeadersInit = {};
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
    });

    return this.handleResponse<T>(response);
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse<T>(response);
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    // For delete operations, handle both success responses and empty responses
    if (!response.ok) {
      const errorData: ApiError = await response.json().catch(() => ({
        error: `HTTP ${response.status}: ${response.statusText}`,
      }));
      throw new Error(errorData.error || 'Delete operation failed');
    }

    // For successful deletes, return the response data or a success indicator
    try {
      const data: ApiResponse<T> = await response.json();
      return data.data || data as T;
    } catch {
      // If no JSON response (e.g., 204 No Content), return success indicator
      return { success: true } as T;
    }
  }
}

export const apiService = new ApiService(API_BASE_URL);

// Auth API
export const authApi = {
  login: (email: string, password: string) =>
    apiService.post<{ user: any; token: string }>('/auth/login', { email, password }),
  
  register: (data: any) =>
    apiService.post<{ user: any }>('/auth/register', data),
  
  getProfile: () =>
    apiService.get<any>('/auth/profile'),
  
  updateProfile: (data: any) =>
    apiService.put<any>('/auth/profile', data),
  
  changePassword: (currentPassword: string, newPassword: string) =>
    apiService.put<void>('/auth/change-password', { currentPassword, newPassword }),
  
  logout: () =>
    apiService.post<void>('/auth/logout'),

  // Invitation OTP methods
  sendInvitationOTP: (token: string) =>
    apiService.post<{ message: string }>('/invitations/send-otp', { token }),

  verifyInvitationOTP: (token: string, otp: string) =>
    apiService.post<{ message: string }>('/invitations/verify-otp', { token, otp }),

  // Set password for invitation acceptance
  setInvitationPassword: (data: { token: string; name: string; password: string; phone: string }) =>
    apiService.post<{ user: any }>('/auth/set-invitation-password', data),

  // Forgot password flow
  forgotPassword: (email: string) =>
    apiService.post<{ message: string; token: string }>('/auth/forgot-password', { email }),

  verifyOtp: (token: string, otp: string) =>
    apiService.post<{ message: string; valid: boolean }>('/auth/verify-otp', { token, otp }),

  resetPassword: (token: string, otp: string, newPassword: string) =>
    apiService.post<{ message: string }>('/auth/reset-password', { token, otp, newPassword }),
};

// Users API
export const usersApi = {
  getUsers: (params?: Record<string, string>) =>
    apiService.get<any[]>('/users', params),

  getUserById: (id: string) =>
    apiService.get<any>(`/users/${id}`),

  createUser: (data: any) =>
    apiService.post<any>('/users', data),

  updateUser: (id: string, data: any) =>
    apiService.put<any>(`/users/${id}`, data),

  deleteUser: (id: string) =>
    apiService.delete<void>(`/users/${id}`),

  getUserStats: () =>
    apiService.get<any>('/users/stats'),
};

// Invitations API
export const invitationsApi = {
  sendInvitation: (data: any) =>
    apiService.post<any>('/invitations', data),
  
  getInvitations: (params?: Record<string, string>) =>
    apiService.get<any[]>('/invitations', params),
  
  getInvitationById: (id: string) =>
    apiService.get<any>(`/invitations/${id}`),
  
  getInvitationByToken: (token: string) =>
    apiService.get<any>(`/invitations/token/${token}`),

  getStudentProfileForInvitation: (email: string) =>
    apiService.get<any>(`/invitations/student-profile/${encodeURIComponent(email)}`),
  
  cancelInvitation: (id: string) =>
    apiService.put<void>(`/invitations/${id}/cancel`),
  
  resendInvitation: (id: string) =>
    apiService.post<any>(`/invitations/${id}/resend`),
};

// Tree Plantings API
export const treePlantingsApi = {
  createTreePlanting: (formData: FormData) =>
    apiService.postFormData<any>('/tree-plantings', formData),
  
  getTreePlantings: (params?: Record<string, string>) =>
    apiService.get<any[]>('/tree-plantings', params),
  
  getTreePlantingById: (id: string) =>
    apiService.get<any>(`/tree-plantings/${id}`),
  
  updateTreePlanting: (id: string, data: any) =>
    apiService.put<any>(`/tree-plantings/${id}`, data),
  
  verifyTreePlanting: (id: string, data: any) =>
    apiService.put<any>(`/tree-plantings/${id}/verify`, data),
  
  deleteTreePlanting: (id: string) =>
    apiService.delete<void>(`/tree-plantings/${id}`),
  
  getTreePlantingStats: () =>
    apiService.get<any>('/tree-plantings/stats'),
};

// Colleges API
export const collegesApi = {
  createCollege: (data: any) =>
    apiService.post<any>('/colleges', data),
  
  getColleges: (params?: Record<string, string>) =>
    apiService.get<any[]>('/colleges', params),
  
  getCollegeById: (id: string) =>
    apiService.get<any>(`/colleges/${id}`),
  
  updateCollege: (id: string, data: any) =>
    apiService.put<any>(`/colleges/${id}`, data),
  
  deleteCollege: (id: string) =>
    apiService.delete<void>(`/colleges/${id}`),
  
  getCollegeStats: (id: string) =>
    apiService.get<any>(`/colleges/${id}/stats`),

  getCollegeComparison: () =>
    apiService.get<any[]>('/colleges/comparison'),

  getCollegeDepartmentComparison: (id: string) =>
    apiService.get<any[]>(`/colleges/${id}/department-comparison`),

  getCollegeAllStudents: (id: string) =>
    apiService.get<any[]>(`/colleges/${id}/all-students`),
};

// Departments API
export const departmentsApi = {
  createDepartment: (data: any) =>
    apiService.post<any>('/departments', data),
  
  getDepartments: (params?: Record<string, string>) =>
    apiService.get<any[]>('/departments', params),
  
  getDepartmentById: (id: string) =>
    apiService.get<any>(`/departments/${id}`),
  
  updateDepartment: (id: string, data: any) =>
    apiService.put<any>(`/departments/${id}`, data),
  
  deleteDepartment: (id: string) =>
    apiService.delete<void>(`/departments/${id}`),
  
  getDepartmentStats: (id: string) =>
    apiService.get<any>(`/departments/${id}/stats`),

  getDepartmentComparison: (id: string) =>
    apiService.get<any[]>(`/departments/${id}/comparison`),
};

// Courses API
export const coursesApi = {
  createCourse: (data: any) =>
    apiService.post<any>('/courses', data),

  getCourses: (params?: Record<string, string>) =>
    apiService.get<any[]>('/courses', params),

  getCourseById: (id: string) =>
    apiService.get<any>(`/courses/${id}`),

  updateCourse: (id: string, data: any) =>
    apiService.put<any>(`/courses/${id}`, data),

  deleteCourse: (id: string) =>
    apiService.delete<void>(`/courses/${id}`),

  getCourseStats: (id: string) =>
    apiService.get<any>(`/courses/${id}/stats`),
};

// Staff/Users API
export const staffApi = {
  createStaff: (data: any) =>
    apiService.post<any>('/users', data),

  getStaff: (params?: Record<string, string>) =>
    apiService.get<any[]>('/users', params),

  getStaffById: (id: string) =>
    apiService.get<any>(`/users/${id}`),

  updateStaff: (id: string, data: any) =>
    apiService.put<any>(`/users/${id}`, data),

  deleteStaff: (id: string) =>
    apiService.delete<void>(`/users/${id}`),

  getStaffStats: () =>
    apiService.get<any>('/users/stats'),

  sendLoginDetails: (data: { email: string; name: string; password: string; role: string }) =>
    apiService.post<any>('/users/send-login-details', data),
};

// Student Profiles API
export const studentProfilesApi = {
  createStudentProfile: (data: any) =>
    apiService.post<any>('/students/profiles', data),

  getStudentProfiles: (params?: Record<string, string>) =>
    apiService.get<any[]>('/students/profiles', params),

  getStudentProfileById: (id: string) =>
    apiService.get<any>(`/students/profiles/${id}`),

  updateStudentProfile: (id: string, data: any) =>
    apiService.put<any>(`/students/profiles/${id}`, data),

  deleteStudentProfile: (id: string) =>
    apiService.delete<void>(`/students/profiles/${id}`),
};

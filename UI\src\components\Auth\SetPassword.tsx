import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Lock, Eye, EyeOff, CheckCircle, Mail, User, BookOpen, Hash, Calendar } from 'lucide-react';
import { useInvitationByToken } from '../../hooks/useInvitationQueries';
import { authApi, invitationsApi } from '../../services/api';
import { useToast } from '../UI/Toast';

export default function SetPassword() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const toast = useToast();

  const { data: invitation, isLoading: invitationLoading, error: invitationError } = useInvitationByToken(token || '');

  const [formData, setFormData] = useState({
    name: '',
    password: '',
    confirmPassword: '',
    phone: '',
    class: '',
    semester: '',
    rollNumber: '',
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'otp' | 'password'>('otp'); // Start with OTP verification
  const [otp, setOtp] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  const [studentProfile, setStudentProfile] = useState<any>(null);
  const [profileLoading, setProfileLoading] = useState(false);

  useEffect(() => {
    if (!token) {
      navigate('/login');
    }
  }, [token, navigate]);

  // Fetch student profile data when invitation is loaded and it's for a student
  useEffect(() => {
    const fetchStudentProfile = async () => {
      if (invitation && invitation.role === 'student' && invitation.email) {
        setProfileLoading(true);
        try {
          const response = await invitationsApi.getStudentProfileForInvitation(invitation.email);
          const profile = response.data;

          // Pre-populate form with student profile data
          if (profile) {
            setFormData(prev => ({
              ...prev,
              name: profile.name || '',
              phone: profile.phone || '',
              class: profile.class || '',
              semester: profile.semester || '',
              rollNumber: profile.rollNumber || '',
            }));
          }

          setStudentProfile(profile);
          if (profile) {
            console.log('Student profile loaded:', profile);
          }
        } catch (error) {
          console.log('No existing student profile found, user will need to enter details manually');
          // This is not an error - student might not have a pre-created profile
        } finally {
          setProfileLoading(false);
        }
      }
    };

    fetchStudentProfile();
  }, [invitation]);

  const handleSendOTP = async () => {
    if (!token) return;

    setLoading(true);
    setError(null);

    try {
      await authApi.sendInvitationOTP(token);
      setOtpSent(true);
      toast.success('OTP sent to your email address');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send OTP';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOTP = async () => {
    if (!token || !otp) return;

    setLoading(true);
    setError(null);

    try {
      await authApi.verifyInvitationOTP(token, otp);
      setStep('password');
      toast.success('Email verified successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Invalid OTP';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!token || !invitation) return;

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    // For non-student roles, validate the form fields
    if (invitation.role !== 'student') {
      if (!formData.name.trim()) {
        setError('Name is required');
        return;
      }

      if (!formData.phone.trim()) {
        setError('Phone number is required');
        return;
      }
    }

    setLoading(true);

    try {
      if (invitation.role === 'student') {
        // Use register API for students - use profile data if available, otherwise form data
        await authApi.register({
          token,
          name: studentProfile?.name || formData.name,
          password: formData.password,
          phone: studentProfile?.phone || formData.phone,
          class: studentProfile?.class || formData.class,
          semester: studentProfile?.semester || formData.semester,
          rollNumber: studentProfile?.rollNumber || formData.rollNumber,
        });
      } else {
        // Use setInvitationPassword API for other roles
        await authApi.setInvitationPassword({
          token,
          name: formData.name,
          password: formData.password,
          phone: formData.phone,
        });
      }

      toast.success('Password set successfully! You can now login with your credentials.');
      navigate('/login');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to set password';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (invitationLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
      </div>
    );
  }

  if (invitationError || !invitation) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Invalid Invitation</h2>
          <p className="text-gray-600 mb-6">
            {invitationError instanceof Error ? invitationError.message : invitationError || 'This invitation link is invalid or has expired.'}
          </p>
          <button
            onClick={() => navigate('/login')}
            className="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full space-y-8 bg-white p-6 rounded-lg">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 bg-green-600 rounded-full flex items-center justify-center">
            <Lock className="h-6 w-6 text-white" />
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            {step === 'otp' ? 'Verify Your Email' : 'Set Your Password'}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Welcome as <span className="font-semibold capitalize">{invitation.role}</span>
          </p>
          <p className="text-sm text-gray-500">
            Email: {invitation.email}
          </p>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {step === 'otp' ? (
          <div className="space-y-6">
            <div className="text-center">
              <Mail className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-600 mb-6">
                We need to verify your email address before you can set your password.
              </p>
            </div>

            {!otpSent ? (
              <button
                onClick={handleSendOTP}
                disabled={loading}
                className="w-full bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Sending OTP...' : 'Send Verification Code'}
              </button>
            ) : (
              <div className="space-y-4">
                <div>
                  <label htmlFor="otp" className="block text-sm font-medium text-gray-700 mb-2">
                    Enter 6-digit verification code
                  </label>
                  <input
                    id="otp"
                    name="otp"
                    type="text"
                    maxLength={6}
                    value={otp}
                    onChange={(e) => setOtp(e.target.value.replace(/\D/g, ''))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500 text-center text-lg tracking-widest"
                    placeholder="000000"
                  />
                </div>
                <button
                  onClick={handleVerifyOTP}
                  disabled={loading || otp.length !== 6}
                  className="w-full bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Verifying...' : 'Verify Code'}
                </button>
                <button
                  onClick={handleSendOTP}
                  disabled={loading}
                  className="w-full text-green-600 py-2 px-4 rounded-md hover:bg-green-50 disabled:opacity-50"
                >
                  Resend Code
                </button>
              </div>
            )}
          </div>
        ) : (
          <>
            {invitation?.role === 'student' && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-700">
                  ✅ Your profile information is already on file.
                  Simply set your password to complete the registration.
                </p>
              </div>
            )}
            <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Only show profile fields for non-student roles */}
              {invitation?.role !== 'student' && (
                <>
                  {/* Name */}
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      Full Name
                    </label>
                    <div className="mt-1 relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        id="name"
                        name="name"
                        type="text"
                        required
                        value={formData.name}
                        onChange={handleInputChange}
                        className="appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                        placeholder="Enter your full name"
                      />
                    </div>
                  </div>

                  {/* Phone */}
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                      Phone Number
                    </label>
                    <input
                      id="phone"
                      name="phone"
                      type="tel"
                      required
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                      placeholder="Phone number"
                    />
                  </div>
                </>
              )}

              {/* Password */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    className="appearance-none relative block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              {/* Confirm Password */}
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                  Confirm Password
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    required
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="appearance-none relative block w-full px-3 py-2 pl-10 pr-7 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Confirm Password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full mt-6 bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {loading ? (
                'Setting Password...'
              ) : (
                <>
                  <CheckCircle className="h-5 w-5 mr-2" />
                  {invitation?.role === 'student' ? 'Set Password & Complete Registration' : 'Set Password & Complete Setup'}
                </>
              )}
            </button>
          </form>
          </>
        )}
      </div>
    </div>
  );
}
